from django.shortcuts import render
from rest_framework import viewsets, permissions
from .serializers import *
from .models import *
from rest_framework.response import Response
from django.contrib.auth import get_user_model
User = get_user_model()

from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from .data_validation import DatasetValidation
from rest_framework.decorators import action
from .ml_service import MLPredictionService
from rest_framework import status

class RegisterViewset(viewsets.ViewSet):
    permission_classes = [permissions.AllowAny]
    queryset = User.objects.all()
    serializer_class = RegisterSerializer

    def create(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        else:
            return Response(serializer.errors, status=400)
        
class UserViewset(viewsets.ViewSet):
    permission_classes = [permissions.IsAuthenticated]
    queryset = User.objects.all()
    serializer_class = RegisterSerializer

    def list(self, request):
        queryset = User.objects.all()
        serializer = self.serializer_class(queryset, many=True)
        return Response(serializer.data)
    
class DatasetViewset(viewsets.ViewSet):
    permission_classes = [permissions.AllowAny]
    queryset = Dataset.objects.all()
    serializer_class = DatasetSerializer
    parser_classes = [MultiPartParser, FormParser]

    def list(self, request):
        queryset = Dataset.objects.all()
        serializer = self.serializer_class(queryset, many=True)
        return Response(serializer.data)

    def create(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        else:
            return Response(serializer.errors, status=400)
    
    def retrieve(self, request, pk=None):
        queryset = Dataset.objects.get(pk=pk)
        serializer = self.serializer_class(queryset)
        return Response(serializer.data)
        
class DataValidationViewset(viewsets.ViewSet):
    parser_classes = [MultiPartParser, FormParser]

    def list(self, request):
        # Generate URL for actions
        validate_url = self.reverse_action(self.validate.url_name)

        return Response({'dataset validation': validate_url})

    @action(detail=False, methods=['post'])
    def validate(self, request):
        file = request.FILES.get('file')
        if not file:
            return Response({'error': 'No file provided'}, status=400)
        
        is_valid, message = DatasetValidation.validate(file)
        
        if not is_valid:
            return Response({'valid': False, 'message': message}, status=400)
        
        return Response({'valid': True, 'message': message})

class PredictionViewset(viewsets.ViewSet):
    permission_classes = [permissions.AllowAny]  # You can change this to IsAuthenticated if needed

    def list(self, request):
        """List all prediction results"""
        queryset = PredictionResult.objects.all().order_by('-created_at')
        serializer = PredictionResultSerializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk=None):
        """Get specific prediction result"""
        try:
            prediction = PredictionResult.objects.get(pk=pk)
            serializer = PredictionResultSerializer(prediction)
            return Response(serializer.data)
        except PredictionResult.DoesNotExist:
            return Response({'error': 'Prediction not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def generate(self, request):
        """Generate ML predictions for a dataset"""
        dataset_id = request.data.get('dataset_id')

        if not dataset_id:
            return Response(
                {'error': 'dataset_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Initialize ML service and run prediction pipeline
            ml_service = MLPredictionService()
            result = ml_service.run_prediction_pipeline(dataset_id)

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def by_dataset(self, request):
        """Get predictions by dataset ID"""
        dataset_id = request.query_params.get('dataset_id')

        if not dataset_id:
            return Response(
                {'error': 'dataset_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            predictions = PredictionResult.objects.filter(
                dataset_id=dataset_id
            ).order_by('year', 'industry_sector')

            serializer = PredictionResultSerializer(predictions, many=True)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def trends(self, request):
        """Get trend data with rankings"""
        dataset_id = request.query_params.get('dataset_id')
        trend_type = request.query_params.get('type', 'short-term')  # short-term, mid-term, long-term

        if not dataset_id:
            return Response(
                {'error': 'dataset_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            trends = Trend.objects.filter(
                prediction_result__dataset_id=dataset_id,
                type=trend_type,
                is_latest=True
            ).select_related('prediction_result').order_by('rank')

            serializer = TrendSerializer(trends, many=True)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )