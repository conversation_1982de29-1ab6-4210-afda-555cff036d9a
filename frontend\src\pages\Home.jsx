import Navbar from "../components/NavBar";
import UploadDataset from "../components/modals/UploadDataset";
import { useEffect, useState } from "react";
import "../css/Home.css";
import Card from "../components/Card";
import AxiosInstance from "../components/AxiosInstance";
import authService from "../services/authService";
import predictionService from "../services/predictionService";

export default function Home() {
  const [isUploadDataset, setUploadDataset] = useState(false);
  const [activeFilter, setActiveFilter] = useState("Growing Industry Sector");
  const [growthSampleData, setGrowthSampleData] = useState([]);
  const [revenueSampleData, setRevenueSampleData] = useState([]);
  const [leastSaturatedSampleData, setLeastSaturatedSampleData] = useState([]);
  const [title, setTitle] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);

  // New state for dynamic predictions
  const [dynamicPredictions, setDynamicPredictions] = useState({
    shortTerm: [],
    midTerm: [],
    longTerm: [],
  });
  const [latestDatasetId, setLatestDatasetId] = useState(null);
  const [isLoadingPredictions, setIsLoadingPredictions] = useState(false);
  const [useDynamicData, setUseDynamicData] = useState(false);

  // Get the current user.
  useEffect(() => {
    const fetchCurrentUser = async () => {
      const getCurrentUser = await authService.getCurrrentUser();
      setCurrentUser(getCurrentUser);
    };

    fetchCurrentUser();
  }, []);

  // Fethch the sample data from the JSON files.
  useEffect(() => {
    fetch("Industry-Growth-Predictions.json")
      .then((response) => response.json())
      .then((json) => setGrowthSampleData(json))
      .catch((error) => console.error("Error loading growth data: ", error));
    fetch("Industry-Revenue-Predictions.json")
      .then((response) => response.json())
      .then((json) => setRevenueSampleData(json))
      .catch((error) => console.error("Error loading revenue data: ", error));
    fetch("Least-Crowded-Industry-Predictions.json")
      .then((response) => response.json())
      .then((json) => setLeastSaturatedSampleData(json))
      .catch((error) =>
        console.error("Error loading least saturated data: ", error)
      );
  }, []);

  // Load dynamic predictions from the latest dataset
  const loadDynamicPredictions = async (datasetId) => {
    if (!datasetId) return;

    setIsLoadingPredictions(true);
    try {
      // Fetch trends for all three time periods
      const [shortTermTrends, midTermTrends, longTermTrends] =
        await Promise.all([
          predictionService.getTrends(datasetId, "short-term"),
          predictionService.getTrends(datasetId, "mid-term"),
          predictionService.getTrends(datasetId, "long-term"),
        ]);

      // Transform the data to match frontend format
      const transformedData = {
        shortTerm: predictionService.transformTrendData(shortTermTrends),
        midTerm: predictionService.transformTrendData(midTermTrends),
        longTerm: predictionService.transformTrendData(longTermTrends),
      };

      setDynamicPredictions(transformedData);
      setUseDynamicData(true);
      console.log("Dynamic predictions loaded:", transformedData);
    } catch (error) {
      console.error("Failed to load dynamic predictions:", error);
      // Fall back to sample data if dynamic loading fails
      setUseDynamicData(false);
    } finally {
      setIsLoadingPredictions(false);
    }
  };

  // Handle prediction completion from upload modal
  const handlePredictionComplete = (datasetId, predictionResponse) => {
    console.log("Predictions completed for dataset:", datasetId);
    setLatestDatasetId(datasetId);
    // Load the new predictions
    loadDynamicPredictions(datasetId);
  };

  // Set the title based on the active filter.
  useEffect(() => {
    if (activeFilter == "Growing Industry Sector") {
      setTitle("Top 5 Growing Industry Sectors in 2026");
    } else if (activeFilter == "Industry Sector Revenue") {
      setTitle("Top 5 Industry Sectors by Revenue in 2026");
    } else {
      setTitle(
        "Top 5 Least Crowded Industry Sectors in 2026 (Based on Business Count)"
      );
    }
  }, [activeFilter]);

  // Set this to prevent the user from scrolling when the modal is open.
  if (isUploadDataset) {
    document.body.style.overflow = "hidden"; // Prevent the user from scrolling when the modal is open.
  } else {
    document.body.style.overflow = "auto"; // Ensure the user can scroll when the modal is close.
  }

  // Function to get the sample data based on the active filter, type, and top number.
  const sampleData = (type, topNumber, filterResult) => {
    // Use dynamic data if available, otherwise fall back to sample data
    if (useDynamicData && dynamicPredictions) {
      let dataSource = [];

      // Get the appropriate data source based on type
      if (type === "short-term") {
        dataSource = dynamicPredictions.shortTerm;
      } else if (type === "mid-term") {
        dataSource = dynamicPredictions.midTerm;
      } else if (type === "long-term") {
        dataSource = dynamicPredictions.longTerm;
      }

      // Filter and sort based on the active filter
      let filteredData = [...dataSource];

      if (filterResult === "Growing Industry Sector") {
        // Sort by growth rate descending
        filteredData.sort((a, b) => b.predictedGrowth - a.predictedGrowth);
      } else if (filterResult === "Industry Sector Revenue") {
        // Sort by revenue descending
        filteredData.sort((a, b) => b.predictedRevenue - a.predictedRevenue);
      } else if (filterResult === "Least Crowded") {
        // Sort by number of businesses ascending (least crowded first)
        filteredData.sort(
          (a, b) => a.predictedNumBusinesses - b.predictedNumBusinesses
        );
      }

      // Return the item at the specified rank (topNumber)
      return filteredData[topNumber - 1] || null;
    }

    // Fall back to static sample data
    if (filterResult == "Growing Industry Sector") {
      return growthSampleData.find(
        (item) => item.type === type && item.rank === topNumber
      );
    } else if (filterResult == "Industry Sector Revenue") {
      return revenueSampleData.find(
        (item) => item.type === type && item.rank === topNumber
      );
    } else if (filterResult == "Least Crowded") {
      return leastSaturatedSampleData.find(
        (item) => item.type === type && item.rank === topNumber
      );
    }
  };

  return (
    <>
      {isUploadDataset && (
        <UploadDataset
          showModal={() => setUploadDataset(false)}
          onPredictionComplete={handlePredictionComplete}
        />
      )}

      <nav>
        <Navbar showModal={() => setUploadDataset(true)} />
      </nav>
      <main className="home">
        {/* Data source indicator */}
        <div className="data-source-indicator">
          {isLoadingPredictions ? (
            <div className="loading-indicator">
              <div className="spinner"></div>
              <span>Loading your predictions...</span>
            </div>
          ) : useDynamicData ? (
            <div className="dynamic-data-indicator">
              <span>📊 Showing predictions from your uploaded dataset</span>
            </div>
          ) : (
            <div className="sample-data-indicator">
              <span>
                📋 Showing sample data - Upload your dataset to see personalized
                predictions
              </span>
            </div>
          )}
        </div>

        <section className="short-term" id="short-term">
          <div className="filter">
            <p>Filter Results</p>
            <div className="line">
              <div
                className={`dot ${
                  activeFilter == "Growing Industry Sector" ? "active" : ""
                }`}
                onClick={() => setActiveFilter("Growing Industry Sector")}
              >
                <span>Growing Industry Sector</span>
              </div>
            </div>
            <div className="line">
              <div
                className={`dot ${
                  activeFilter == "Industry Sector Revenue" ? "active" : ""
                }`}
                onClick={() => setActiveFilter("Industry Sector Revenue")}
              >
                <span>Industry Sector Revenue</span>
              </div>
            </div>
            <div className="line">
              <div
                className={`dot ${
                  activeFilter == "Least Crowded" ? "active" : ""
                }`}
                onClick={() => setActiveFilter("Least Crowded")}
              >
                <span>Least Crowded</span>
              </div>
            </div>
          </div>
          <h1>Short-Term Outlook: {title}</h1>
          <section className="short-term-contents">
            <Card
              topNumber={4}
              type="short-term"
              data={sampleData("short-term", 4, activeFilter)}
              filterResult={activeFilter}
            />
            <Card
              topNumber={2}
              type="short-term"
              data={sampleData("short-term", 2, activeFilter)}
              filterResult={activeFilter}
            />
            <Card
              topNumber={1}
              type="short-term"
              data={sampleData("short-term", 1, activeFilter)}
              filterResult={activeFilter}
            />
            <Card
              topNumber={3}
              type="short-term"
              data={sampleData("short-term", 3, activeFilter)}
              filterResult={activeFilter}
            />
            <Card
              topNumber={5}
              type="short-term"
              data={sampleData("short-term", 5, activeFilter)}
              filterResult={activeFilter}
            />
          </section>
        </section>

        {/* Mid-term Trends */}
        <section className="mid-term" id="mid-term">
          <h1>Mid-Term Outlook: {title}</h1>
          <section className="mid-term-contents">
            <Card
              topNumber={4}
              type="mid-term"
              data={sampleData("mid-term", 4, activeFilter)}
              filterResult={activeFilter}
              color="dark"
            />
            <Card
              topNumber={2}
              type="mid-term"
              data={sampleData("mid-term", 2, activeFilter)}
              filterResult={activeFilter}
              color="dark"
            />
            <Card
              topNumber={1}
              type="mid-term"
              data={sampleData("mid-term", 1, activeFilter)}
              filterResult={activeFilter}
              color="dark"
            />
            <Card
              topNumber={3}
              type="mid-term"
              data={sampleData("mid-term", 3, activeFilter)}
              filterResult={activeFilter}
              color="dark"
            />
            <Card
              topNumber={5}
              type="mid-term"
              data={sampleData("mid-term", 5, activeFilter)}
              filterResult={activeFilter}
              color="dark"
            />
          </section>
        </section>

        {/* Long-term Trends */}
        <section className="long-term" id="long-term">
          <h1>Long-Term Outlook: {title}</h1>
          <section className="long-term-contents">
            <Card
              topNumber={4}
              type="long-term"
              data={sampleData("long-term", 4, activeFilter)}
              filterResult={activeFilter}
            />
            <Card
              topNumber={2}
              type="long-term"
              data={sampleData("long-term", 2, activeFilter)}
              filterResult={activeFilter}
            />
            <Card
              topNumber={1}
              type="long-term"
              data={sampleData("long-term", 1, activeFilter)}
              filterResult={activeFilter}
            />
            <Card
              topNumber={3}
              type="long-term"
              data={sampleData("long-term", 3, activeFilter)}
              filterResult={activeFilter}
            />
            <Card
              topNumber={5}
              type="long-term"
              data={sampleData("long-term", 5, activeFilter)}
              filterResult={activeFilter}
            />
          </section>
        </section>
      </main>
    </>
  );
}
