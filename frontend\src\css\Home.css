/* Data source indicators */
.data-source-indicator {
  position: sticky;
  top: 0;
  z-index: 5;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 12px 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  font-weight: 500;
}

.loading-indicator .spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.dynamic-data-indicator {
  color: white;
  font-weight: 500;
  text-align: center;
}

.sample-data-indicator {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  text-align: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.home {
  display: flex;
  flex-direction: column;
  height: auto;
  width: auto;
  justify-content: center;
  align-items: center;
  color: var(--primary-dark-color);
  margin-top: 60px;
  position: relative;
}

.home .filter {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 30%;
  right: 38px;
  position: fixed;
  z-index: 1;
  gap: 8px;
}

.home .filter p {
  color: rgba(245, 245, 249, 0.8);
  font-size: 12px;

  margin-bottom: 10px;
  padding: 2px 8px;
  border-radius: 5px;

  background-color: var(--primary-dark-color);
}

.home .filter .line {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;
  height: 100px;
  width: 2px;
  border-radius: 50px;
  background-color: var(--btn-color);
  position: relative;
}

.line .dot {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 8px;
  width: 8px;
  border-radius: 50px;
  background-color: transparent;
  box-shadow: 0 0 0 2px var(--btn-color);
  font-size: 10px;
  position: absolute;
  top: -8px;
  cursor: pointer;
  transition: 0.5s ease;
}

.line .dot.active {
  box-shadow: unset;
  height: 8px;
  width: 8px;
  background-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 1), 0 0 0 4px rgba(79, 70, 229, 0.3),
    0 0 0 6px rgba(79, 70, 229, 0.3), 0 0 80px 30px rgba(79, 70, 229, 0.3);
}

.line .dot.active span {
  color: white;
  background-color: var(--btn-color);
}

.dot span {
  color: rgba(255, 255, 255, 0.5);
  position: absolute;
  bottom: -55px;
  text-align: center;
  background-color: var(--primary-dark-color);
  box-shadow: 0 0 0 1px var(--btn-color);
  border-radius: 10px;
  padding: 2px 10px;
  transition: 0.5s ease;
}

/* Style the last span element under the filter className */
.filter .line:nth-child(4) span:nth-child(1) {
  bottom: -42px;
}

.home
  section:nth-child(odd):not(
    .short-term-contents,
    .mid-term-contents,
    .long-term-contents
  ) {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  height: 100vh;
  gap: 38px;
  width: 100%;
  background-color: var(--primary-dark-color);
  padding: 38px 135px 38px 80px;
}

.home
  section:nth-child(even):not(
    .short-term-contents,
    .mid-term-contents,
    .long-term-contents
  ) {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  height: 100vh;
  gap: 20px;
  width: 100%;
  background-color: var(--primary-color);
  padding: 38px 135px 38px 80px;
  color: var(--primary-dark-color);
}

.short-term h1,
.long-term h1 {
  color: var(--primary-color);
}

.home h1:not(.industry) {
  padding: 0px 50px 0px 50px;
  font-size: 26px;
  text-align: center;
  align-self: center;
}

.short-term-contents,
.mid-term-contents,
.long-term-contents {
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: start;
  gap: 60px;
}

/* Cuztomize the scrollbar */
/* Target the scrollbar */
::-webkit-scrollbar {
  width: 10px; /* Width of the vertical scrollbar */
  height: 10px; /* Height of the horizontal scrollbar */
}

/* Scrollbar track */
::-webkit-scrollbar-track {
  background: rgba(240, 240, 240, 0.3); /* Color of the scrollbar track */
  border-radius: 10px; /* Rounded corners */
}

/* Scrollbar thumb */
::-webkit-scrollbar-thumb {
  background-color: var(--btn-color); /* Color of the scrollbar thumb */
  border-radius: 10px; /* Rounded corners */
  border: 2px solid #f0f0f0; /* Padding around the thumb */
}

/* Scrollbar thumb on hover */
::-webkit-scrollbar-thumb:hover {
  background-color: #0056b3; /* Darker color when hovering */
}
