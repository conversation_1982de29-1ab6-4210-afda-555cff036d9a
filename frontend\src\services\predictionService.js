import authService from "./authService";

const API_URL = "http://127.0.0.1:8000/predictions/";

class PredictionService {
  // Generate ML predictions for a dataset
  async generatePredictions(datasetId) {
    try {
      const response = await fetch(`${API_URL}generate/`, {
        method: "POST",
        headers: {
          ...this.getAuthHeader(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ dataset_id: datasetId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate predictions");
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Failed to generate predictions:", error);
      throw error;
    }
  }

  // Get predictions by dataset ID
  async getPredictionsByDataset(datasetId) {
    try {
      const response = await fetch(`${API_URL}by_dataset/?dataset_id=${datasetId}`, {
        method: "GET",
        headers: this.getAuthHeader(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch predictions");
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Failed to fetch predictions:", error);
      throw error;
    }
  }

  // Get trend data with rankings
  async getTrends(datasetId, trendType = "short-term") {
    try {
      const response = await fetch(
        `${API_URL}trends/?dataset_id=${datasetId}&type=${trendType}`,
        {
          method: "GET",
          headers: this.getAuthHeader(),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch trends");
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Failed to fetch trends:", error);
      throw error;
    }
  }

  // Get all predictions
  async getAllPredictions() {
    try {
      const response = await fetch(API_URL, {
        method: "GET",
        headers: this.getAuthHeader(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch all predictions");
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Failed to fetch all predictions:", error);
      throw error;
    }
  }

  // Transform backend prediction data to match frontend format
  transformPredictionData(predictions, filterType) {
    return predictions.map((prediction, index) => {
      const baseData = {
        id: prediction.id,
        year: prediction.year,
        industrySector: prediction.industry_sector,
        predictedRevenue: prediction.predicted_revenue,
        predictedGrowth: prediction.predicted_growth_rate,
        predictedNumBusinesses: prediction.predicted_least_crowded, // This represents number of businesses
        rank: index + 1, // Will be properly ranked by backend
        type: this.getTypeFromYear(prediction.year),
      };

      // Add category based on filter type
      if (filterType === "Growing Industry Sector") {
        baseData.category = "growing industry sector";
      } else if (filterType === "Industry Sector Revenue") {
        baseData.category = "revenue industry sector";
      } else if (filterType === "Least Crowded") {
        baseData.category = "least crowded";
      }

      return baseData;
    });
  }

  // Transform trend data to match frontend format
  transformTrendData(trends) {
    return trends.map((trend) => ({
      id: trend.prediction_result.id,
      year: trend.prediction_result.year,
      industrySector: trend.prediction_result.industry_sector,
      predictedRevenue: trend.prediction_result.predicted_revenue,
      predictedGrowth: trend.prediction_result.predicted_growth_rate,
      predictedNumBusinesses: trend.prediction_result.predicted_least_crowded,
      rank: trend.rank,
      type: trend.type,
      category: this.getCategoryFromType(trend.type),
    }));
  }

  // Helper method to determine type from year
  getTypeFromYear(year) {
    const currentYear = new Date().getFullYear();
    const yearDiff = year - currentYear;

    if (yearDiff <= 1) return "short-term";
    if (yearDiff <= 3) return "mid-term";
    return "long-term";
  }

  // Helper method to get category from type
  getCategoryFromType(type) {
    const categoryMap = {
      "short-term": "growing industry sector",
      "mid-term": "revenue industry sector", 
      "long-term": "least crowded"
    };
    return categoryMap[type] || "growing industry sector";
  }

  // Get the access token
  getAccessToken() {
    return localStorage.getItem("access_token");
  }

  // Get the Authorization headers
  getAuthHeader() {
    const token = this.getAccessToken();
    return {
      Authorization: token ? `JWT ${token}` : "",
    };
  }
}

const predictionService = new PredictionService();

export default predictionService;
